# Bybit Gleam Client

[![Package Version](https://img.shields.io/hexpm/v/bybit)](https://hex.pm/packages/bybit)
[![Hex Docs](https://img.shields.io/badge/hex-docs-ffaff3)](https://hexdocs.pm/bybit/)

A comprehensive Gleam client library for the [Bybit](https://www.bybit.com/) cryptocurrency exchange API. This library provides a functional programming interface to interact with Bybit's REST API v5, including market data, trading, account management, and more.

## Features

- ✅ **Complete V5 REST API Support** - Full coverage of Bybit's V5 API endpoints
- ✅ **Type-Safe** - Leverages Gleam's type system for compile-time safety
- ✅ **Functional Programming** - Idiomatic Gleam code with immutable data structures
- ✅ **Error Handling** - Comprehensive error handling using Gleam's Result type
- ✅ **Authentication** - HMAC-SHA256 request signing for private endpoints
- ✅ **Rate Limiting** - Built-in rate limit parsing and handling
- ✅ **Testnet Support** - Easy switching between live and test environments
- ✅ **Demo Trading** - Support for Bybit's demo trading environment

## Installation

```sh
gleam add bybit@1
```

## Quick Start

### Public API (No Authentication Required)

```gleam
import bybit/rest_client_v5
import bybit/types
import gleam/io

pub fn main() -> Nil {
  // Create client with default options
  let options = types.default_rest_client_options()
  let client = rest_client_v5.new_rest_client_v5(options)

  // Get server time
  case rest_client_v5.get_server_time(client) {
    Ok(response) -> {
      io.println("Server time: " <> response.result.time_second)
    }
    Error(error) -> {
      io.println("Error: " <> error)
    }
  }
}
```

### Private API (Authentication Required)

```gleam
import bybit/rest_client_v5
import bybit/types
import gleam/option
import gleam/io

pub fn main() -> Nil {
  // Create authenticated client
  let options = types.RestClientOptions(
    ..types.default_rest_client_options(),
    key: option.Some("your_api_key"),
    secret: option.Some("your_api_secret"),
    testnet: True,  // Use testnet for development
  )
  let client = rest_client_v5.new_rest_client_v5(options)

  // Get account information
  case rest_client_v5.get_account_info(client) {
    Ok(response) -> {
      case types.is_success(response) {
        True -> io.println("Account info retrieved successfully")
        False -> io.println("API error: " <> response.ret_msg)
      }
    }
    Error(error) -> {
      io.println("Request failed: " <> error)
    }
  }
}
```

## API Coverage

This library provides comprehensive coverage of Bybit's V5 API:

### Market Data
- ✅ Server time
- ✅ Instruments info
- ✅ Orderbook
- ✅ Tickers
- ✅ Klines/Candlesticks
- ✅ Recent trades
- ✅ Open interest
- ✅ Historical volatility
- ✅ Insurance fund
- ✅ Risk limit
- ✅ Delivery price
- ✅ Long/short ratio

### Trading
- ✅ Place order
- ✅ Amend order
- ✅ Cancel order
- ✅ Cancel all orders
- ✅ Batch orders
- ✅ Order history
- ✅ Open orders
- ✅ Trade history
- ✅ Execution list

### Account Management
- ✅ Account info
- ✅ Wallet balance
- ✅ Fee rates
- ✅ Account coin balance
- ✅ Borrow history
- ✅ Collateral info
- ✅ Asset info
- ✅ All coins balance

### Position Management
- ✅ Position info
- ✅ Set leverage
- ✅ Switch margin mode
- ✅ Set TP/SL mode
- ✅ Switch position mode
- ✅ Set risk limit
- ✅ Set trading stop
- ✅ Add/reduce margin

### Demo Trading
- ✅ Create demo account
- ✅ Request demo funds

## Configuration Options

```gleam
import bybit/types
import gleam/option

let options = types.RestClientOptions(
  key: option.Some("your_api_key"),
  secret: option.Some("your_api_secret"),
  testnet: False,                    // Use live environment
  demo_trading: False,               // Use demo trading
  recv_window: 5000,                 // Request window in ms
  enable_time_sync: False,           // Auto time synchronization
  timeout: 300_000,                  // Request timeout in ms
  api_region: types.Default,         // API region
  parse_exceptions: True,            // Parse error responses
  parse_api_rate_limits: False,      // Parse rate limit headers
  // ... other options
)
```

## Error Handling

The library uses Gleam's `Result` type for comprehensive error handling:

```gleam
case rest_client_v5.get_server_time(client) {
  Ok(response) -> {
    // Check if API call was successful
    case types.is_success(response) {
      True -> {
        // Handle successful response
        io.println("Success: " <> response.ret_msg)
      }
      False -> {
        // Handle API error
        io.println("API Error " <> int.to_string(response.ret_code) <> ": " <> response.ret_msg)
      }
    }
  }
  Error(error) -> {
    // Handle network/parsing errors
    io.println("Request failed: " <> error)
  }
}
```

## Examples

See the `examples/` directory for comprehensive usage examples:

- `examples/basic_usage.gleam` - Basic client usage and common operations

## Development

```sh
# Install dependencies
gleam deps download

# Run tests
gleam test

# Format code
gleam format

# Run examples
gleam run -m examples/basic_usage
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the Apache-2.0 License - see the LICENSE file for details.

## Disclaimer

This library is not officially affiliated with Bybit. Use at your own risk. Always test thoroughly with testnet before using with real funds.
