/// Basic usage examples for the Bybit Gleam client
/// 
/// This file demonstrates how to use the Bybit client library
/// for common operations like getting market data and managing orders.
import gleam/int
import gleam/io
import gleam/json
import gleam/option
import gleam/result
import gleam/string

import bybit/constants
import bybit/rest_client_v5
import bybit/types

/// Example: Create a client and get server time
pub fn example_get_server_time() -> Nil {
  // Create client with default options (no authentication needed for public endpoints)
  let options = types.default_rest_client_options()
  let client = rest_client_v5.new_rest_client_v5(options)

  // Get server time
  case rest_client_v5.get_server_time(client) {
    Ok(response) -> {
      io.println("Server time retrieved successfully:")
      io.println("Time (seconds): " <> response.result.time_second)
      io.println("Time (nanoseconds): " <> response.result.time_nano)
    }
    Error(error) -> {
      io.println("Failed to get server time: " <> error)
    }
  }
}

/// Example: Create a client with API credentials
pub fn example_create_authenticated_client() -> rest_client_v5.RestClientV5 {
  let options =
    types.RestClientOptions(
      ..types.default_rest_client_options(),
      key: option.Some("your_api_key_here"),
      secret: option.Some("your_api_secret_here"),
      testnet: True,
      // Use testnet for development
      recv_window: 5000,
    )

  rest_client_v5.new_rest_client_v5(options)
}

/// Example: Check latency to Bybit servers
pub fn example_check_latency() -> Nil {
  let options = types.default_rest_client_options()
  let client = rest_client_v5.new_rest_client_v5(options)

  case rest_client_v5.fetch_latency_summary(client) {
    Ok(latency) -> {
      io.println("Latency Summary:")
      io.println(
        "Round trip time: " <> int.to_string(latency.round_trip_time) <> "ms",
      )
      io.println(
        "Estimated one-way latency: "
        <> int.to_string(latency.estimated_one_way_latency)
        <> "ms",
      )
      io.println(
        "Time difference: " <> int.to_string(latency.time_difference) <> "ms",
      )

      case latency.time_difference > 500 || latency.time_difference < -500 {
        True -> {
          io.println("WARNING: Time difference is greater than 500ms!")
          io.println("Consider synchronizing your system clock.")
        }
        False -> {
          io.println("Time synchronization is within acceptable range.")
        }
      }
    }
    Error(error) -> {
      io.println("Failed to check latency: " <> error)
    }
  }
}

/// Example: Get account information (requires authentication)
pub fn example_get_account_info() -> Nil {
  let client = example_create_authenticated_client()

  case rest_client_v5.get_account_info(client) {
    Ok(response) -> {
      case types.is_success(response) {
        True -> {
          io.println("Account information retrieved successfully")
          // In a real application, you would parse the JSON result
          io.println("Account data: " <> json.to_string(response.result))
        }
        False -> {
          io.println("API returned error: " <> response.ret_msg)
        }
      }
    }
    Error(error) -> {
      io.println("Failed to get account info: " <> error)
    }
  }
}

/// Example: Get wallet balance (requires authentication)
pub fn example_get_wallet_balance() -> Nil {
  let client = example_create_authenticated_client()

  let params =
    types.GetWalletBalanceParams(
      account_type: option.Some("UNIFIED"),
      coin: option.None,
      // Get all coins
    )

  case rest_client_v5.get_wallet_balance(client, option.Some(params)) {
    Ok(response) -> {
      case types.is_success(response) {
        True -> {
          io.println("Wallet balance retrieved successfully")
          io.println("Balance data: " <> json.to_string(response.result))
        }
        False -> {
          io.println("API returned error: " <> response.ret_msg)
        }
      }
    }
    Error(error) -> {
      io.println("Failed to get wallet balance: " <> error)
    }
  }
}

/// Example: Create a demo account (requires authentication)
pub fn example_create_demo_account() -> Nil {
  let client = example_create_authenticated_client()

  case rest_client_v5.create_demo_account(client) {
    Ok(response) -> {
      case types.is_success(response) {
        True -> {
          io.println("Demo account created successfully")
          io.println("Sub Member ID: " <> response.result.sub_member_id)
        }
        False -> {
          io.println("Failed to create demo account: " <> response.ret_msg)
        }
      }
    }
    Error(error) -> {
      io.println("Failed to create demo account: " <> error)
    }
  }
}

/// Example: Request demo trading funds (requires authentication)
pub fn example_request_demo_funds() -> Nil {
  let client = example_create_authenticated_client()

  let demo_funds = [
    rest_client_v5.DemoApplyMoney(coin: "USDT", amount_str: "10000"),
    rest_client_v5.DemoApplyMoney(coin: "BTC", amount_str: "1"),
  ]

  let params =
    rest_client_v5.DemoTradingFundsParams(
      adjust_type: option.Some(1),
      uta_demo_apply_money: option.Some(demo_funds),
    )

  case rest_client_v5.request_demo_trading_funds(client, option.Some(params)) {
    Ok(response) -> {
      case types.is_success(response) {
        True -> {
          io.println("Demo trading funds requested successfully")
        }
        False -> {
          io.println("Failed to request demo funds: " <> response.ret_msg)
        }
      }
    }
    Error(error) -> {
      io.println("Failed to request demo funds: " <> error)
    }
  }
}

/// Example: Working with constants and enums
pub fn example_constants_usage() -> Nil {
  io.println("=== Bybit Constants Examples ===")

  // API Error Codes
  let success_code = constants.api_error_code_to_int(constants.Success)
  io.println("Success code: " <> int.to_string(success_code))

  let error_code =
    constants.api_error_code_to_int(constants.InsufficientBalance)
  io.println("Insufficient balance error code: " <> int.to_string(error_code))

  // Position modes
  let one_way_mode =
    constants.linear_position_mode_to_string(constants.OneWayMode)
  io.println("One-way position mode: " <> one_way_mode)

  let hedge_mode = constants.linear_position_mode_to_string(constants.HedgeMode)
  io.println("Hedge position mode: " <> hedge_mode)

  // Position indices
  let one_way_idx =
    constants.linear_position_idx_to_int(constants.OneWayModeIdx)
  io.println("One-way mode index: " <> int.to_string(one_way_idx))

  // Kline intervals
  let interval_1m = types.kline_interval_to_string(types.OneMinute)
  io.println("1 minute interval: " <> interval_1m)

  let interval_1d = types.kline_interval_to_string(types.OneDay)
  io.println("1 day interval: " <> interval_1d)

  // Order sides
  let buy_side = types.order_side_to_string(types.Buy)
  io.println("Buy side: " <> buy_side)

  let sell_side = types.order_side_to_string(types.Sell)
  io.println("Sell side: " <> sell_side)
}

/// Main function to run all examples
pub fn main() -> Nil {
  io.println("=== Bybit Gleam Client Examples ===")
  io.println("")

  // Public API examples (no authentication required)
  io.println("1. Getting server time...")
  example_get_server_time()
  io.println("")

  io.println("2. Checking latency...")
  example_check_latency()
  io.println("")

  io.println("3. Constants usage...")
  example_constants_usage()
  io.println("")

  // Note: The following examples require valid API credentials
  io.println("4. Private API examples (require authentication)...")
  io.println("   - Uncomment and add your API credentials to test these")
  // example_get_account_info()
  // example_get_wallet_balance()
  // example_create_demo_account()
  // example_request_demo_funds()

  io.println("")
  io.println("Examples completed!")
}
