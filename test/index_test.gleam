import gleeunit
import index.{<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Guest, Other, User, greet_user}

pub fn main() -> Nil {
  gleeunit.main()
}

pub fn greet_user_test() {
  let user = User("1", "<PERSON>", De<PERSON>ult)
  let greeting = greet_user(user)

  assert greeting == "Hello, <PERSON>"
}

pub fn greet_user_with_admin_role_test() {
  let admin_user = User("2", "<PERSON>", Admin)
  let greeting = greet_user(admin_user)

  assert greeting == "Hello, <PERSON>"
}

pub fn greet_user_with_guest_role_test() {
  let guest_user = User("3", "<PERSON>", Guest)
  let greeting = greet_user(guest_user)

  assert greeting == "Hello, <PERSON>"
}

pub fn greet_user_with_other_role_test() {
  let other_user = User("4", "<PERSON>", Other("Moderator"))
  let greeting = greet_user(other_user)

  assert greeting == "Hello, <PERSON>"
}
